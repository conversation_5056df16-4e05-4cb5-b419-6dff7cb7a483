<div>
    <form action="{{ route('registration.store', [2025]) }}" method="post">
        @csrf

        <div class="max-w-screen-xl px-4 py-8 mx-auto lg:px-6 sm:py-16 lg:py-24">
            <div class="max-w-7xl mx-auto text-center">
                <h1>{{ $title }}</h1>

                <h3>Vstupné je na oba dny konference.<br>
                    Ceny vstupného jsou uvedeny bez DPH.
                </h3>

                <div class="mt-20 flow-root">
                    <div class="isolate -mt-16 grid max-w-sm grid-cols-1 gap-y-16 sm:mx-auto lg:-mx-8 lg:mt-0 lg:max-w-none lg:grid-cols-3 xl:-mx-4">
                        <s:taxonomy from="ticket_types">
                            <div class="pt-16 lg:px-8 lg:pt-0 xl:px-14 @if($loop->iteration % 3 !== 0) border-r border-white @endif">
                                <h4 id="tier-basic">{{ $title }} <i class="fa-light fa-circle-question"></i></h4>
                                <p class="mt-6 flex items-baseline gap-x-1">
                                    <span class="w-full text-5xl text-center font-semibold tracking-tight text-secondary">{{ $price }} Kč</span>
                                </p>
                                <p class="mt-3 text-sm/6 text-secondary-alt">{{ $description }}</p>
                                <button type="button" wire:click="setTicketType('{{ $slug }}')"
                                        class="small w-full mt-10 default">
                                    Zvolit vstupné
                                </button>
                            </div>
                        </s:taxonomy>
                    </div>
                </div>
            </div>
        </div>

        @if($registrationStep >= 2)
            <div class="w-full bg-secondary">
                <fieldset class="networking">
                    <h4>Plánujete se zúčastnit večerního networkingu?</h4>
                    <div class="radio-group">
                        <div wire:click="setStep(3)">
                            <input id="yes" type="radio" name="networking" />
                            <label for="yes">Ano</label>
                        </div>
                        <div wire:click="setStep(3)">
                            <input id="maybe" type="radio" name="networking">
                            <label for="maybe">Možná</label>
                        </div>
                        <div wire:click="setStep(3)">
                            <input id="no" type="radio" name="networking" />
                            <label for="no">Ne</label>
                        </div>
                    </div>
                </fieldset>
            </div>
        @endif

        @if($registrationStep >= 3)
            <h3 class="mt-4 text-center">Údaje o účastníkovi</h3>
            @for($i = 1; $i <= $members; $i++)
                <div class="user-data">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="relative my-6">
                            <label for="first_name">Jméno *</label>
                            <input id="first_name" type="text" name="first_name[]" placeholder="Jan" required />
                            @error('first_name')<div>{{ $message }}</div>@enderror
                        </div>

                        <div class="relative my-6">
                            <label for="last_name">Příjmení *</label>
                            <input id="last_name" type="text" name="last_name[]" placeholder="Novák" required />
                            @error('last_name')<div>{{ $message }}</div>@enderror
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="relative my-6">
                            <label for="email">E-mail *</label>
                            <input id="email" type="text" name="email[]" placeholder="<EMAIL>" required />
                            @error('email')<div>{{ $message }}</div>@enderror
                        </div>

                        <div class="relative my-6">
                            <label for="tel">Telefon včetně předvolby *</label>
                            <input id="tel" type="text" name="tel[]" placeholder="+420 777 888 999" required />
                            @error('tel')<div>{{ $message }}</div>@enderror
                        </div>
                    </div>

                    <div class="relative my-6">
                        <label for="position">Pracovní pozice na jmenovku *</label>
                        <input id="position" type="text" name="position[]" required />
                        @error('position')<div>{{ $message }}</div>@enderror
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="relative my-6">
                            <label for="company">Firma na jmenovku</label>
                            <input id="company" type="text" name="company[]" />
                            @error('company')<div>{{ $message }}</div>@enderror
                        </div>

                        <div class="relative my-6">
                            <label for="linkedin">Odkaz na profil LinkedIn</label>
                            <input id="linkedin" type="text" name="linkedin[]" />
                            @error('linkedin')<div>{{ $message }}</div>@enderror
                        </div>
                    </div>

                    @if($i === 1)
                        <button type="button" wire:click="addMember" class="default small w-full mt-4">Přidat dalšího účastníka</button>
                    @else
                        <button type="button" wire:click="removeMember" class="default small w-full mt-4">Odebrat účastníka</button>
                    @endif
                </div>
            @endfor

            <div class="mt-12">
                <h3 class="text-center">Fakturační údaje</h3>
                <div class="user-data">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="relative my-6">
                            <label for="billing_company">Firma/Osoba *</label>
                            <input id="billing_company" type="text" name="billing_company" required />
                            @error('billing_company')<div>{{ $message }}</div>@enderror
                        </div>

                        <div class="relative my-6">
                            <label for="billing_street">Ulice a č.p. *</label>
                            <input id="billing_street" type="text" name="billing_street" required />
                            @error('billing_street')<div>{{ $message }}</div>@enderror
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="relative my-6">
                            <label for="billing_ico">IČ</label>
                            <input id="billing_ico" type="text" name="billing_ico" />
                            @error('billing_ico')<div>{{ $message }}</div>@enderror
                        </div>

                        <div class="relative my-6">
                            <label for="billing_dic">DIČ</label>
                            <input id="billing_dic" type="text" name="billing_dic" />
                            @error('billing_dic')<div>{{ $message }}</div>@enderror
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="relative my-6">
                            <label for="billing_city">Město *</label>
                            <input id="billing_city" type="text" name="billing_city" required />
                            @error('billing_city')<div>{{ $message }}</div>@enderror
                        </div>

                        <div class="relative my-6">
                            <label for="billing_psc">PSČ *</label>
                            <input id="billing_psc" type="text" name="billing_psc" />
                            @error('billing_psc')<div>{{ $message }}</div>@enderror
                        </div>
                    </div>

                    <div class="relative my-6">
                        <label for="billing_note">Poznámka</label>
                        <textarea id="billing_note" name="billing_note" rows="4" placeholder="Na základě fakturačních údajů Vám bude zaslána faktura, v případě, že chcete fakturu zaslat později nebo jinou změnu, napište nám to do poznámky."></textarea>
                        @error('billing_note')<div>{{ $message }}</div>@enderror
                    </div>

                    <div class="relative my-6">
                        <label for="billing_promo">Promo kód</label>
                        <input id="billing_promo" type="text" name="billing_promo" />
                        @error('billing_note')<div>{{ $message }}</div>@enderror
                    </div>

                    <div class="relative my-6">
                        <div class="flex items-center mt-4">
                            <input type="checkbox" name="gdpr" id="gdpr" class="mr-2" required />
                            <p>Registrací souhlasíte s <button type="button" wire:click="toggleGdpr" class="cursor-pointer text-secondary hover:underline">našimi zásadami ochrany osobních údajů</button>.</p>
                        </div>
                    </div>

                    @if($showGdpr)
                        <div class="gdpr-window">
                            <h4 class="text-lg font-semibold mb-4">Zásady ochrany osobních údajů</h4>

                            <div class="space-y-3">
                                <p>
                                    <strong>Souhlas se zpracováním osobních údajů:</strong><br>
                                    Registrací vyjadřujete souhlas se zpracováním osobních údajů ve smyslu zákona č. 101/2000 Sb., o ochraně osobních údajů. Uvedené údaje budou využity v souvislosti s registrací na akci.
                                </p>

                                <p>
                                    <strong>Souhlas s pořízením fotografií a videozáznamů:</strong><br>
                                    Dále vyjadřujete souhlas s pořízením a zveřejněním fotografií a videozáznamu na akci. Tyto materiály budou zveřejněny ve fotogalerii na stránkách konference <a href="https://www.pmkonference.cz" class="text-primary hover:underline" target="_blank">www.pmkonference.cz</a> a dále použity k propagaci akce organizátory.
                                </p>

                                <p>
                                    <strong>Správci osobních údajů:</strong><br>
                                    Souhlasím, aby subjekty <strong>Symphera s.r.o.</strong> (se sídlem: U Potoka 26, 252 65 Tursko, Praha – Západ, IČ: 28361296, zapsaná v obchodním rejstříku vedeném Městským soudem v Praze, oddíl C, vložka 136102) nebo <strong>Komora projektových manažerů, z.s.</strong> (se sídlem: U Potoka 26, 252 65 Tursko, Praha – Západ, IČ: 22890335, zapsaná ve spolkovém rejstříku vedeném Městským soudem v Praze, oddíl L, vložka 22900) zpracovávaly mé osobní údaje.
                                </p>

                                <p>
                                    <strong>Rozsah zpracovávaných údajů:</strong><br>
                                    Tato data zahrnují: jméno a příjmení, název společnosti, e-mail a telefonní číslo, získané v souvislosti s poskytováním produktů či služeb (především vzájemnou interakcí Symphera s.r.o. nebo Komora projektových manažerů, z.s., a klienta) nebo potenciálního klienta (například vyhodnocením žádosti klienta, vyhodnocením podmínek pro poskytnutí slevy či jiného benefitu).
                                </p>

                                <div>
                                    <strong>Účely zpracování osobních údajů:</strong>
                                    <ul class="list-disc list-inside mt-2 space-y-1 ml-4">
                                        <li>Následná komunikace a zasílání informací po akci ze strany organizátorů a partnerů</li>
                                        <li>Péče o klienty</li>
                                        <li>Nabízení služeb a produktů spol. Symphera nebo Komora projektových manažerů, z.s.</li>
                                    </ul>
                                </div>

                                <p>
                                    <strong>Odvolání souhlasu:</strong><br>
                                    S výše uvedeným zpracováním udělujete svůj výslovný souhlas. Souhlas je možné kdykoli odvolat zasláním emailu na adresu <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a> nebo <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a>, případně dopisu na kontaktní údaje společností.
                                </p>

                                <div>
                                    <strong>Vaše práva podle zákona o ochraně osobních údajů:</strong>
                                    <ul class="list-disc list-inside mt-2 space-y-1 ml-4">
                                        <li>Vzít souhlas kdykoliv zpět</li>
                                        <li>Požadovat po Sympheře nebo Komoře projektových manažerů informaci o tom, jak a jaké mé osobní údaje zpracovávají, a požadovat vysvětlení ohledně zpracování osobních údajů</li>
                                        <li>Vyžádat si přístup k těmto údajům a nechat je aktualizovat nebo opravit</li>
                                        <li>Požadovat výmaz těchto osobních údajů</li>
                                        <li>V případě pochybností o dodržování povinností souvisejících se zpracováním osobních údajů se mohu obrátit na společnost samotnou nebo na Úřad pro ochranu osobních údajů</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <button type="submit" class="primary w-full mt-4 max-w-4xl mx-auto mb-4">Odeslat registraci</button>
                </div>
            </div>
        @endif
    </form>


    <div class="pointer-events-none fixed inset-x-0 bottom-0 sm:flex sm:justify-center sm:px-6 sm:pb-5 lg:px-8">
        <div class="pointer-events-auto flex items-center justify-between gap-x-6 bg-primary px-6 py-2.5 sm:rounded-xl sm:py-3 sm:pr-3.5 sm:pl-4">
            <nav aria-label="Progress" class="flex items-center justify-center">
                <p class="text-sm font-medium text-white">Registrace - krok {{ $registrationStep }} ze {{ $totalSteps }}</p>
                <ol role="list" class="ml-8 flex items-center space-x-5">
                    @for($i = 1; $i <= $totalSteps; $i++)
                        @if($registrationStep - $i > 0)
                            <li>
                                <button type="button" wire:click="setStep({{ $i }})" value="1" class="block size-2.5 rounded-full bg-secondary"></button>
                            </li>
                        @elseif($registrationStep === $i)
                            <li>
                                <a href="#" aria-current="step" class="relative flex items-center justify-center">
                                <span aria-hidden="true" class="absolute flex size-4.5 p-px">
                                  <span class="size-full rounded-full bg-secondary-light"></span>
                                </span>
                                    <span aria-hidden="true" class="relative block size-2.5 rounded-full bg-secondary"></span>
                                </a>
                            </li>
                        @else
                            <li>
                                <a href="#" class="block size-2.5 rounded-full bg-gray-200"></a>
                            </li>
                        @endif
                    @endfor
                </ol>
            </nav>

        </div>
    </div>
</div>
