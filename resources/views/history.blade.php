@extends('layout')

@section('content')
    <div class="max-w-screen-xl px-4 py-8 mx-auto lg:px-6 sm:py-16 lg:py-24">
        <div class="max-w-7xl mx-auto text-center">
            <h1>
                {{ $title }}
            </h1>

            <section class="history">
                <!-- Line -->
                <div class="line"></div>

                <!-- Timeline Items -->
                @php
                    $bgColors   = ['bg-primary-alt', 'bg-secondary', 'bg-primary-light'];
                    $textColors = ['text-secondary-light', 'text-xdark', 'text-muted'];
                @endphp

                <div class="space-y-8 lg:space-y-16">
                    <s:collection:pages blueprint:is="year" sort="title:desc" title:isnt="{{ $base->current_year }}">
                        @php $current = ['text' => $textColors[$loop->iteration % 3], 'bg' => $bgColors[$loop->iteration % 3]]; @endphp

                        <!-- Timeline Item -->
                        <div class="relative flex flex-col lg:flex-row items-center {{ $loop->even ? 'lg:flex-row-reverse' : '' }}">
                            <!-- Year Badge (Desktop) -->
                            <div class="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 top-1/2 z-10 hidden lg:block">
                                <div class="badge">
                                    {{ $title }}
                                </div>
                            </div>

                            <!-- Content Card -->
                            <div class="w-full lg:w-5/12 {{ $loop->even ? 'lg:pr-16' : 'lg:pl-16' }}">
                                <div class="relative {{ $current['bg'] }} rounded-3xl p-4 text-white shadow-xl">
                                    <!-- Year Badge (Mobile) -->
                                    <div class="lg:hidden absolute -top-4 left-6">
                                        <div class="badge">
                                            {{ $title }}
                                        </div>
                                    </div>

                                    <!-- Content -->
                                    <div class="pt-4 lg:pt-0">
                                        <h5 class="{{ $current['text'] }}">{{ $number }}. ročník</h5>
                                        <h4 class="{{ $current['text'] }}">{{ $subtitle }}</h4>

                                        <!-- Action Buttons -->
                                        <div class="flex flex-wrap gap-3">
                                            @if($url)
                                                <a href="{{ $url }}" target="_blank"
                                                   class="pill {{ $current['text'] }}">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                                    </svg>
                                                    WEB
                                                </a>
                                            @endif

                                            @foreach($galleries ?? [] as $item)
                                                <a href="{{ $item->link }}" target="_blank"
                                                   class="pill {{ $current['text'] }}">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                                    </svg>
                                                    GALERIE
                                                </a>
                                            @endforeach

                                            @if($video)
                                                <a href="{{ $video }}" target="_blank"
                                                   class="pill {{ $current['text'] }}">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" aria-hidden="true">
                                                        <rect x="3" y="5" width="18" height="14" rx="2" ry="2"></rect>
                                                        <path d="M10 9.5v5l4.5-2.5-4.5-2.5z" fill="currentColor" stroke="none"></path>
                                                    </svg>
                                                    VIDEO
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Spacer for opposite side -->
                            <div class="hidden lg:block w-5/12"></div>
                        </div>
                    </s:collection:pages>
                </div>
            </section>
        </div>
    </div>
@endsection
