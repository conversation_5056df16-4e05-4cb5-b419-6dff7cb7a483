<nav class="bg-black/30 shadow-sm" x-data="{ mobileMenuOpen: false, profileMenuOpen: false }">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="relative flex h-16 justify-between">
            <div class="absolute inset-y-0 right-0 flex items-center md:hidden">
                <!-- Mobile menu button -->
                <button type="button" @click="mobileMenuOpen = !mobileMenuOpen" :aria-expanded="mobileMenuOpen" class="menu-button" aria-controls="mobile-menu">
                    <span class="absolute -inset-0.5"></span>
                    <svg x-show="!mobileMenuOpen" class="block size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                    </svg>
                    <svg x-show="mobileMenuOpen" class="block size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex flex-1 items-center justify-center sm:items-stretch sm:justify-between">
                <a href="/" class="flex shrink-0 items-center">
                    <img class="h-8 w-auto md:hidden lg:block" src="{{ asset('images/logo.svg') }}" alt="Transformation summit" />
                    <img class="h-8 w-auto hidden md:block lg:hidden" src="{{ asset('images/logo-icon.svg') }}" alt="Transformation summit" />
                </a>
                <div class="hidden md:ml-6 md:flex md:space-x-8">
                    @foreach($nav as $item)
                        <a href="{{ $item['url'] }}" class="@if($item['active'])navbar-item-active @else navbar-item @endif">{{ $item['title'] }}</a>
                    @endforeach
                </div>
            </div>
            <div class="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">
                <x-locales :class="'hidden md:flex md:flex-col'" />
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state. -->
    <div class="md:hidden" id="mobile-menu" x-show="mobileMenuOpen"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95" style="display: none;"
    >
        <div class="space-y-1 pt-2 pb-4">
            @foreach($nav as $item)
                <a href="{{ $item['url'] }}" class="@if($item['active'])border-primary text-secondary @else border-transparent text-gray-300 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 @endif block border-l-4 py-2 pr-4 pl-3 text-base font-medium">{{ $item['title'] }}</a>
            @endforeach
            <x-locales />
        </div>
    </div>
</nav>
