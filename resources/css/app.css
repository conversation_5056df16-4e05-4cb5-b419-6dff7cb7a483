@import "tailwindcss";
@plugin "@tailwindcss/typography";

@theme {
    --color-xdark: #13302C;
    --color-primary: #175347;
    --color-primary-alt: #325B53;
    --color-primary-light: #C7DCCB;
    --color-secondary: #BC9B6E;
    --color-secondary-alt: #DFD5BC;
    --color-secondary-light: #F5EDD9;

    --color-muted: #212121;

    --spacing: 4px;
}

@source inline('{sm:,md:,lg:,xl:,}grid-cols-{1,2,3,4,5,6,7,8,9,10,11,12}');

@layer base {
    * {
        font-family: "Montserrat", serif;
        scroll-behavior: smooth;
    }

    main {
        @apply bg-[#141414] bg-[length:100%_auto] bg-top bg-no-repeat md:bg-[url(/images/background.svg)] max-md:bg-gradient-to-b max-md:from-[#41866D] max-md:via-[#165447] max-md:via-[#12302E] max-md:to-[#141414] max-md:h-[1000px];
    }

    h1 {
        @apply text-secondary text-balance font-bold md:text-[48px] mb-4;
    }

    h2 {
        @apply text-secondary-alt font-bold text-2xl sm:text-3xl md:text-4xl lg:text-5xl mb-4;
    }

    h3 {
        @apply text-secondary-light font-semibold text-xl sm:text-2xl md:text-3xl mb-3;
    }

    h4 {
        @apply text-white font-semibold text-lg sm:text-xl md:text-2xl mb-3;
    }

    h5 {
        @apply text-white font-medium text-base sm:text-lg md:text-xl mb-2;
    }

    h6 {
        @apply text-white font-medium text-sm sm:text-base md:text-lg mb-1;
    }

    button {
        @apply cursor-pointer;
    }

    a {
        @apply text-primary-light;
    }

    p, span {
        @apply text-gray-500 dark:text-gray-400;
    }

    .set-bg-\[#13302C\] {
        @apply bg-xdark;
    }
}

@utility footer {
    @apply bg-[#171717];

    & i {
        @apply mr-2
    }
    & .list-item {
        @apply text-sm/6 text-gray-400 hover:text-white flex items-center
    }
}

@utility footer-container {
    @apply mx-auto max-w-7xl px-6 pt-6 pb-4 sm:pt-12 sm:pb-8 lg:px-8 lg:pt-18 lg:pb-12;

    & > div:not(:last-child) {
        @apply xl:grid xl:grid-cols-3 xl:gap-8;
    }

    & h3 {
        @apply font-normal text-[14px] text-[#A3A3A3]
    }

    & a, & i {
        @apply text-[14px] text-[#E5E7EB] hover:text-gray-400;
    }

    & .bottom {
        @apply mt-16 border-t border-white/10 pt-8 sm:mt-20 flex items-center justify-center lg:mt-24;
    }

    & .bottom p {
        @apply mt-8 text-sm/6 text-gray-400 md:order-1 md:mt-0;
    }

    #newsletter-form {
        @apply mt-6 sm:flex sm:max-w-md;

        & input[type="email"] {
            @apply w-full min-w-0 rounded-md bg-white/5 px-3 py-1.5 text-base text-white outline-1 -outline-offset-1 outline-gray-700 placeholder:text-gray-500 focus:outline-2 focus:-outline-offset-2 focus:outline-secondary sm:w-64 sm:text-sm/6 xl:w-full;
        }
    }
}

@utility menu-button {
    @apply relative inline-flex items-center justify-center rounded-md p-2 text-white focus:outline-hidden focus:ring-inset
}

@utility button-base {
    @apply px-[24px] py-[12px] text-[28px] text-sm;
}

@utility button-small {
    @apply px-3 py-2 text-center text-sm/6 font-semibold;
}

@layer components {
    button.primary, a.primary {
        @apply button-base text-black bg-white hover:bg-white/90;
    }
    button.default, a.default {
        @apply button-base text-white bg-transparent border-2 border-white hover:bg-white/5;
    }
    button.muted, a.muted {
        @apply button-base text-[20px] text-white bg-transparent;
    }

    button.small, a.small {
        @apply button-small;
    }

    .navbar-item {
        @apply inline-flex items-center border-b-2 border-transparent px-1 pt-1 text-sm font-medium text-white hover:border-secondary hover:text-white;
    }
    .navbar-item-active {
        @apply inline-flex items-center border-b-2 border-secondary px-1 pt-1 text-sm font-medium text-secondary;
    }

    .content {
        @apply mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 text-base/7 sm:grid-cols-3 sm:gap-y-16 lg:mx-0 lg:max-w-none;
    }

    /* SOCIALS */
    .social {
        @apply text-gray-400 hover:text-primary-light;
    }

    /* HISTORY */
    section.history {
        @apply relative max-w-6xl mx-auto mt-16;
    }

    section.history .line {
        @apply absolute left-1/2 transform -translate-x-0.5 w-1 bg-primary-light h-full hidden lg:block;
    }

    section.history .badge {
        @apply bg-muted text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg;
    }

    section.history .pill {
        @apply inline-flex items-center gap-2 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full px-4 py-2 text-sm font-medium transition-colors duration-200;
    }

    /* REGISTRATION */
    .networking {
        @apply max-w-7xl mx-auto text-center py-6 mb-12;
    }
    .networking .radio-group {
        @apply mt-6 space-y-6 sm:flex sm:justify-evenly sm:items-center sm:space-y-0 sm:space-x-10;
    }
    .networking .radio-group div {
        @apply flex items-center;
    }
    .networking .radio-group input {
        @apply relative size-4 appearance-none rounded-full border border-gray-300 bg-white before:absolute before:inset-1 before:rounded-full before:bg-white not-checked:before:hidden checked:border-primary checked:bg-primary focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary disabled:border-gray-300 disabled:bg-gray-100 disabled:before:bg-gray-400 forced-colors:appearance-auto forced-colors:before:hidden;
    }
    .networking .radio-group label {
        @apply relative ml-3 block text-sm/6 font-medium text-black;
    }

    .user-data {
        @apply max-w-4xl mx-auto mb-4;
    }
    .user-data label {
        @apply absolute -top-2 left-2 bg-xdark inline-block rounded-lg px-1 text-xs font-medium text-white;
    }

    .user-data input:not([type="checkbox"]),
    .user-data textarea {
        @apply block w-full rounded-md bg-xdark px-3 py-1.5 text-base text-white outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-secondary sm:text-sm/6;
    }

    .gdpr-window {
        @apply bg-primary p-6 rounded-lg border border-gray-200 text-sm text-white space-y-4;
    }
    .gdpr-window * {
        @apply text-white!;
    }

    /* MARKDOWN SETTINGS */
    .markdown {
        @apply flex flex-col;
    }

    .markdown {
        @apply text-base leading-relaxed text-gray-800 space-y-4;

        & p {
            @apply my-2;
        }

        & ul {
            @apply pl-6;
        }

        & li {
            @apply mb-1;
        }

        & strong {
            @apply font-semibold text-white;
        }
    }
}
